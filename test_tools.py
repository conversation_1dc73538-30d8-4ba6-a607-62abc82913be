#!/usr/bin/env python3
"""
Test script to verify all tools are working correctly
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.tools.bash_tool import bash
from src.tools.grep_tool import grep
from src.tools.glob_tool import glob
from src.tools.write_tool import write
from src.tools.edit_tool import edit
from src.tools.web_tool import web


async def test_bash_tool():
    """Test bash tool"""
    print("🔧 Testing bash tool...")
    result = await bash("echo 'Hello from bash tool!'")
    print(f"Success: {result['success']}")
    if result['success']:
        print("✅ Bash tool working")
    else:
        print("❌ Bash tool failed")
    print()


async def test_write_tool():
    """Test write tool"""
    print("🔧 Testing write tool...")
    test_content = "# Test File\nThis is a test file created by the write tool.\n"
    result = await write("test_output.txt", test_content)
    print(f"Success: {result['success']}")
    if result['success']:
        print("✅ Write tool working")
    else:
        print("❌ Write tool failed")
    print()


async def test_glob_tool():
    """Test glob tool"""
    print("🔧 Testing glob tool...")
    result = await glob("*.py")
    print(f"Success: {result['success']}")
    if result['success']:
        print("✅ Glob tool working")
    else:
        print("❌ Glob tool failed")
    print()


async def test_grep_tool():
    """Test grep tool"""
    print("🔧 Testing grep tool...")
    result = await grep("test", "*.py")
    print(f"Success: {result['success']}")
    if result['success']:
        print("✅ Grep tool working")
    else:
        print("❌ Grep tool failed")
    print()


async def test_edit_tool():
    """Test edit tool"""
    print("🔧 Testing edit tool...")
    # First create a file to edit
    await write("test_edit.txt", "Original content\nLine 2\n")
    
    # Now edit it
    result = await edit("test_edit.txt", "Original", "Modified")
    print(f"Success: {result['success']}")
    if result['success']:
        print("✅ Edit tool working")
    else:
        print("❌ Edit tool failed")
    print()


async def test_web_tool():
    """Test web tool"""
    print("🔧 Testing web tool...")
    result = await web("Python programming", max_results=2)
    print(f"Success: {result['success']}")
    if result['success']:
        print("✅ Web tool working")
    else:
        print("❌ Web tool failed")
    print()


async def main():
    """Run all tool tests"""
    print("🚀 Arien AI - Tool Testing Suite")
    print("=" * 40)
    print()
    
    tests = [
        test_bash_tool,
        test_write_tool,
        test_glob_tool,
        test_grep_tool,
        test_edit_tool,
        test_web_tool
    ]
    
    for test in tests:
        try:
            await test()
        except Exception as e:
            print(f"❌ {test.__name__} failed with error: {e}")
            print()
    
    print("🏁 Tool testing completed!")
    
    # Cleanup
    try:
        Path("test_output.txt").unlink(missing_ok=True)
        Path("test_edit.txt").unlink(missing_ok=True)
    except:
        pass


if __name__ == "__main__":
    asyncio.run(main())
