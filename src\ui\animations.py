"""
Animation components for the CLI interface
"""
import time
import asyncio
from typing import Optional
from rich.console import Console
from rich.live import Live
from rich.text import Text
from rich.panel import Panel
from rich.align import Align


class BallAnimation:
    """Custom ball animation with elapsed time"""
    
    def __init__(self, console: Optional[Console] = None):
        self.console = console or Console()
        self.frames = [
            "( ●    )",
            "(  ●   )",
            "(   ●  )",
            "(    ● )",
            "(     ●)",
            "(    ● )",
            "(   ●  )",
            "(  ●   )",
            "( ●    )",
            "(●     )",
        ]
        self.current_frame = 0
        self.start_time = None
        self.is_running = False
    
    def start(self):
        """Start the animation timer"""
        self.start_time = time.time()
        self.is_running = True
    
    def stop(self):
        """Stop the animation"""
        self.is_running = False
    
    def get_frame(self) -> str:
        """Get current animation frame with elapsed time"""
        if not self.is_running:
            return "●"
        
        # Get current frame
        frame = self.frames[self.current_frame]
        self.current_frame = (self.current_frame + 1) % len(self.frames)
        
        # Calculate elapsed time
        elapsed = time.time() - self.start_time if self.start_time else 0
        elapsed_str = f"{elapsed:.1f}s"
        
        return f"{frame} {elapsed_str}"
    
    async def animate_with_text(self, text: str, duration: Optional[float] = None):
        """Animate with text for a specific duration"""
        self.start()
        
        with Live(console=self.console, refresh_per_second=10) as live:
            start_time = time.time()
            
            while self.is_running:
                if duration and (time.time() - start_time) >= duration:
                    break
                
                frame = self.get_frame()
                display_text = Text()
                display_text.append(frame, style="cyan bold")
                display_text.append(f" {text}", style="white")
                
                live.update(display_text)
                await asyncio.sleep(0.1)
        
        self.stop()


class ProgressIndicator:
    """Progress indicator for long-running operations"""
    
    def __init__(self, console: Optional[Console] = None):
        self.console = console or Console()
        self.ball_animation = BallAnimation(console)
    
    async def show_progress(self, message: str, task_coro):
        """Show progress animation while running a task"""
        # Start animation
        animation_task = asyncio.create_task(
            self.ball_animation.animate_with_text(message)
        )
        
        try:
            # Run the actual task
            result = await task_coro
            return result
        finally:
            # Stop animation
            self.ball_animation.stop()
            animation_task.cancel()
            try:
                await animation_task
            except asyncio.CancelledError:
                pass
    
    def show_status(self, message: str, status: str = "info"):
        """Show a status message with appropriate styling"""
        if status == "success":
            icon = "✅"
            style = "green bold"
        elif status == "error":
            icon = "❌"
            style = "red bold"
        elif status == "warning":
            icon = "⚠️"
            style = "yellow bold"
        elif status == "info":
            icon = "ℹ️"
            style = "blue bold"
        else:
            icon = "●"
            style = "white"
        
        text = Text()
        text.append(f"{icon} ", style=style)
        text.append(message, style="white")
        
        self.console.print(text)
    
    def show_panel(self, content: str, title: str = "", border_style: str = "blue"):
        """Show content in a bordered panel"""
        panel = Panel(
            content,
            title=title,
            border_style=border_style,
            padding=(1, 2)
        )
        self.console.print(panel)


class StreamingDisplay:
    """Display for streaming AI responses"""
    
    def __init__(self, console: Optional[Console] = None):
        self.console = console or Console()
        self.current_text = ""
    
    async def stream_text(self, text_generator, prefix: str = "🤖 "):
        """Stream text as it's generated"""
        self.current_text = ""
        
        with Live(console=self.console, refresh_per_second=10) as live:
            async for chunk in text_generator:
                self.current_text += chunk
                
                # Create display text
                display_text = Text()
                display_text.append(prefix, style="cyan bold")
                display_text.append(self.current_text, style="white")
                
                live.update(display_text)
                await asyncio.sleep(0.01)  # Small delay for smooth streaming
    
    def show_final_text(self, text: str, prefix: str = "🤖 "):
        """Show final text without streaming"""
        display_text = Text()
        display_text.append(prefix, style="cyan bold")
        display_text.append(text, style="white")
        self.console.print(display_text)


class WelcomeScreen:
    """Welcome screen and branding"""
    
    def __init__(self, console: Optional[Console] = None):
        self.console = console or Console()
    
    def show_welcome(self):
        """Display welcome screen"""
        welcome_text = """
[bold cyan]🤖 Arien AI - Advanced Terminal Assistant[/bold cyan]

[white]Powered by AI with intelligent function calling capabilities[/white]
[dim]Never give up • Retry logic • Real-time streaming[/dim]

[yellow]Available Tools:[/yellow]
• [green]bash[/green]  - Execute shell commands
• [green]grep[/green]  - Search file contents  
• [green]glob[/green]  - Find files by pattern
• [green]write[/green] - Create/update files
• [green]edit[/green]  - Modify existing files
• [green]web[/green]   - Search the internet

[blue]Type your request and let AI handle the rest![/blue]
        """
        
        panel = Panel(
            welcome_text,
            title="[bold]Welcome to Arien AI[/bold]",
            border_style="cyan",
            padding=(1, 2)
        )
        
        self.console.print(Align.center(panel))
        self.console.print()
    
    def show_setup_required(self):
        """Show setup required message"""
        setup_text = """
[yellow]⚙️  Setup Required[/yellow]

Before using Arien AI, you need to configure an LLM provider.

[white]Available providers:[/white]
• [green]Deepseek[/green] - Cloud-based AI (requires API key)
• [green]Ollama[/green]   - Local AI models

[blue]Run the setup to get started![/blue]
        """
        
        panel = Panel(
            setup_text,
            title="[bold]Configuration Needed[/bold]",
            border_style="yellow",
            padding=(1, 2)
        )
        
        self.console.print(Align.center(panel))
        self.console.print()


# Global instances
console = Console()
ball_animation = BallAnimation(console)
progress_indicator = ProgressIndicator(console)
streaming_display = StreamingDisplay(console)
welcome_screen = WelcomeScreen(console)
