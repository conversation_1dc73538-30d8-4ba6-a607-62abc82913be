"""
Glob Tool - File pattern matching and discovery
"""
import os
import glob as glob_module
from typing import Dict, Any, List, Optional
from pathlib import Path
import fnmatch


class GlobTool:
    """File pattern matching tool for finding files by name patterns"""
    
    def __init__(self):
        self.max_results = 1000
    
    async def find_files(self, pattern: str, directory: Optional[str] = None, 
                        recursive: bool = True, include_dirs: bool = False,
                        max_results: Optional[int] = None) -> Dict[str, Any]:
        """
        Find files matching name patterns
        
        Args:
            pattern: File name pattern (supports wildcards)
            directory: Directory to search in (default: current)
            recursive: Whether to search recursively
            include_dirs: Whether to include directories in results
            max_results: Maximum number of results
            
        Returns:
            Dict with found files and metadata
        """
        try:
            # Set search directory
            search_dir = directory or os.getcwd()
            if not os.path.exists(search_dir):
                return {
                    "success": False,
                    "error": f"Directory not found: {search_dir}",
                    "files": []
                }
            
            # Find matching files
            files = self._find_matching_files(pattern, search_dir, recursive, include_dirs)
            
            # Sort by modification time (newest first)
            files.sort(key=lambda f: f.get('mtime', 0), reverse=True)
            
            # Limit results
            max_res = max_results or self.max_results
            limited_files = files[:max_res]
            
            return {
                "success": True,
                "pattern": pattern,
                "directory": search_dir,
                "recursive": recursive,
                "total_found": len(files),
                "returned": len(limited_files),
                "files": limited_files
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Search error: {str(e)}",
                "files": []
            }
    
    def _find_matching_files(self, pattern: str, directory: str, 
                           recursive: bool, include_dirs: bool) -> List[Dict[str, Any]]:
        """Find files matching the pattern"""
        files = []
        
        try:
            if recursive:
                # Use glob with recursive pattern
                if '**' not in pattern:
                    search_pattern = os.path.join(directory, '**', pattern)
                else:
                    search_pattern = os.path.join(directory, pattern)
                
                matches = glob_module.glob(search_pattern, recursive=True)
            else:
                # Non-recursive search
                search_pattern = os.path.join(directory, pattern)
                matches = glob_module.glob(search_pattern)
            
            for match in matches:
                try:
                    abs_path = os.path.abspath(match)
                    is_dir = os.path.isdir(abs_path)
                    
                    # Skip directories if not requested
                    if is_dir and not include_dirs:
                        continue
                    
                    # Get file metadata
                    stat_info = os.stat(abs_path)
                    
                    file_info = {
                        "path": abs_path,
                        "relative_path": os.path.relpath(abs_path, directory),
                        "name": os.path.basename(abs_path),
                        "is_directory": is_dir,
                        "size": stat_info.st_size if not is_dir else 0,
                        "mtime": stat_info.st_mtime,
                        "permissions": oct(stat_info.st_mode)[-3:],
                        "extension": os.path.splitext(abs_path)[1] if not is_dir else ""
                    }
                    
                    files.append(file_info)
                    
                except (OSError, PermissionError) as e:
                    # Skip files we can't access
                    files.append({
                        "path": match,
                        "relative_path": match,
                        "name": os.path.basename(match),
                        "is_directory": False,
                        "size": 0,
                        "mtime": 0,
                        "permissions": "000",
                        "extension": "",
                        "error": f"Access denied: {str(e)}"
                    })
        
        except Exception as e:
            # Return empty list on error
            pass
        
        return files
    
    def _format_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"
    
    def _format_time(self, timestamp: float) -> str:
        """Format timestamp to readable string"""
        try:
            import datetime
            dt = datetime.datetime.fromtimestamp(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return "Unknown"


# Tool function for AI agent
async def glob(pattern: str, directory: Optional[str] = None, 
               recursive: bool = True, include_dirs: bool = False) -> Dict[str, Any]:
    """
    Find files matching name patterns
    
    Usage Examples:
    - glob("*.py")                    # Find all Python files
    - glob("config.*", "/etc")        # Find config files in /etc
    - glob("**/*.log", "/var")        # Find all log files recursively
    - glob("test_*.py", recursive=False)  # Non-recursive search
    - glob("*", include_dirs=True)    # Include directories
    
    Args:
        pattern: File name pattern with wildcards (* and ?)
        directory: Directory to search in (default: current directory)
        recursive: Search subdirectories recursively
        include_dirs: Include directories in results
    
    Returns:
        Dictionary with found files and metadata
    """
    tool = GlobTool()
    result = await tool.find_files(pattern, directory, recursive, include_dirs)
    
    # Format output for AI agent
    if result["success"]:
        output = f"📁 File search completed\n"
        output += f"Pattern: {result['pattern']}\n"
        output += f"Directory: {result['directory']}\n"
        output += f"Recursive: {result['recursive']}\n"
        output += f"Found: {result['total_found']} files\n"
        
        if result["returned"] < result["total_found"]:
            output += f"Showing: {result['returned']} files (limited)\n"
        
        output += "\n"
        
        if result["files"]:
            output += "Files (sorted by modification time):\n"
            for i, file_info in enumerate(result["files"][:50], 1):  # Show first 50
                icon = "📁" if file_info["is_directory"] else "📄"
                size = tool._format_size(file_info["size"]) if not file_info["is_directory"] else "DIR"
                mtime = tool._format_time(file_info["mtime"])
                
                output += f"\n{i:3d}. {icon} {file_info['relative_path']}\n"
                output += f"     Size: {size:>10} | Modified: {mtime} | Perms: {file_info['permissions']}\n"
                
                if "error" in file_info:
                    output += f"     ⚠️  {file_info['error']}\n"
            
            if len(result["files"]) > 50:
                output += f"\n... and {len(result['files']) - 50} more files\n"
        else:
            output += "No files found matching the pattern.\n"
    else:
        output = f"❌ File search failed\n"
        output += f"Error: {result['error']}\n"
    
    return {
        "tool": "glob",
        "success": result["success"],
        "output": output,
        "raw_result": result
    }
