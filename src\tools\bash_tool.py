"""
Bash Tool - Interactive command execution
"""
import subprocess
import asyncio
import os
import signal
from typing import Dict, Any, Optional
from pathlib import Path


class BashTool:
    """Interactive bash command execution tool"""
    
    def __init__(self, timeout: int = 300):
        self.timeout = timeout
        self.current_directory = os.getcwd()
        self.environment = os.environ.copy()
    
    async def execute(self, command: str, cwd: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute bash command in interactive shell
        
        Args:
            command: Bash command to execute
            cwd: Working directory (optional)
            
        Returns:
            Dict with stdout, stderr, return_code, and execution info
        """
        try:
            # Set working directory
            work_dir = cwd or self.current_directory
            if cwd:
                self.current_directory = os.path.abspath(cwd)
            
            # Create process
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=work_dir,
                env=self.environment,
                preexec_fn=os.setsid if os.name != 'nt' else None
            )
            
            try:
                # Wait for completion with timeout
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.timeout
                )
                
                return {
                    "success": True,
                    "stdout": stdout.decode('utf-8', errors='replace'),
                    "stderr": stderr.decode('utf-8', errors='replace'),
                    "return_code": process.returncode,
                    "command": command,
                    "cwd": work_dir,
                    "timeout": False
                }
                
            except asyncio.TimeoutError:
                # Kill process on timeout
                if os.name != 'nt':
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                else:
                    process.terminate()
                
                try:
                    await asyncio.wait_for(process.wait(), timeout=5)
                except asyncio.TimeoutError:
                    if os.name != 'nt':
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                    else:
                        process.kill()
                
                return {
                    "success": False,
                    "stdout": "",
                    "stderr": f"Command timed out after {self.timeout} seconds",
                    "return_code": -1,
                    "command": command,
                    "cwd": work_dir,
                    "timeout": True
                }
                
        except Exception as e:
            return {
                "success": False,
                "stdout": "",
                "stderr": f"Execution error: {str(e)}",
                "return_code": -1,
                "command": command,
                "cwd": work_dir,
                "timeout": False
            }
    
    def set_environment_variable(self, key: str, value: str):
        """Set environment variable for future commands"""
        self.environment[key] = value
    
    def get_current_directory(self) -> str:
        """Get current working directory"""
        return self.current_directory
    
    def change_directory(self, path: str) -> bool:
        """Change current working directory"""
        try:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path) and os.path.isdir(abs_path):
                self.current_directory = abs_path
                return True
            return False
        except Exception:
            return False


# Tool function for AI agent
async def bash(command: str, cwd: Optional[str] = None) -> Dict[str, Any]:
    """
    Execute bash command in interactive shell
    
    Usage Examples:
    - bash("ls -la")
    - bash("cd /tmp && pwd")
    - bash("ps aux | grep python")
    - bash("find . -name '*.py' | head -10")
    
    Args:
        command: The bash command to execute
        cwd: Working directory (optional)
    
    Returns:
        Dictionary with execution results
    """
    tool = BashTool()
    result = await tool.execute(command, cwd)
    
    # Format output for AI agent
    if result["success"]:
        output = f"✅ Command executed successfully\n"
        output += f"Command: {result['command']}\n"
        output += f"Directory: {result['cwd']}\n"
        output += f"Return code: {result['return_code']}\n"
        
        if result["stdout"]:
            output += f"\nSTDOUT:\n{result['stdout']}"
        
        if result["stderr"]:
            output += f"\nSTDERR:\n{result['stderr']}"
    else:
        output = f"❌ Command failed\n"
        output += f"Command: {result['command']}\n"
        output += f"Error: {result['stderr']}\n"
        
        if result["timeout"]:
            output += f"⏰ Command timed out after {tool.timeout} seconds\n"
    
    return {
        "tool": "bash",
        "success": result["success"],
        "output": output,
        "raw_result": result
    }


# Global bash tool instance
bash_tool = BashTool()
