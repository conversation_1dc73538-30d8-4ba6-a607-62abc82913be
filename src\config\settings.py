"""
Configuration management for Arien AI CLI
"""
import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class LLMConfig:
    """LLM Provider configuration"""
    provider: str  # 'deepseek' or 'ollama'
    api_key: Optional[str] = None
    model: str = "deepseek-chat"
    base_url: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3


@dataclass
class UIConfig:
    """UI configuration"""
    animation_speed: float = 0.1
    show_progress: bool = True
    color_scheme: str = "default"
    max_output_lines: int = 1000


@dataclass
class ToolConfig:
    """Tool execution configuration"""
    bash_timeout: int = 300
    web_search_results: int = 5
    file_backup: bool = True
    parallel_execution: bool = True


@dataclass
class ArienConfig:
    """Main configuration class"""
    llm: LLMConfig
    ui: UIConfig
    tools: ToolConfig
    debug: bool = False
    log_level: str = "INFO"


class ConfigManager:
    """Manages configuration loading, saving, and validation"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".arien-ai"
        self.config_file = self.config_dir / "config.yaml"
        self.config_dir.mkdir(exist_ok=True)
        
    def load_config(self) -> ArienConfig:
        """Load configuration from file or create default"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    data = yaml.safe_load(f)
                return self._dict_to_config(data)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self._create_default_config()
        else:
            return self._create_default_config()
    
    def save_config(self, config: ArienConfig) -> bool:
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                yaml.dump(asdict(config), f, default_flow_style=False)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def _create_default_config(self) -> ArienConfig:
        """Create default configuration"""
        return ArienConfig(
            llm=LLMConfig(
                provider="deepseek",
                model="deepseek-chat"
            ),
            ui=UIConfig(),
            tools=ToolConfig()
        )
    
    def _dict_to_config(self, data: Dict[str, Any]) -> ArienConfig:
        """Convert dictionary to configuration object"""
        # Ensure LLM config has required fields
        llm_data = data.get('llm', {})
        if 'provider' not in llm_data:
            llm_data['provider'] = 'deepseek'

        # Filter UI config to only include valid fields
        ui_data = data.get('ui', {})
        valid_ui_fields = {'animation_speed', 'show_progress', 'color_scheme', 'max_output_lines'}
        ui_data = {k: v for k, v in ui_data.items() if k in valid_ui_fields}

        # Filter tool config to only include valid fields
        tool_data = data.get('tools', {})
        valid_tool_fields = {'bash_timeout', 'web_search_results', 'file_backup', 'parallel_execution'}
        tool_data = {k: v for k, v in tool_data.items() if k in valid_tool_fields}

        return ArienConfig(
            llm=LLMConfig(**llm_data),
            ui=UIConfig(**ui_data),
            tools=ToolConfig(**tool_data),
            debug=data.get('debug', False),
            log_level=data.get('log_level', 'INFO')
        )
    
    def setup_llm_provider(self) -> ArienConfig:
        """Interactive setup for LLM provider"""
        print("\n🤖 Arien AI - LLM Provider Setup")
        print("=" * 40)
        
        # Choose provider
        print("\nAvailable LLM Providers:")
        print("1. Deepseek (deepseek-chat, deepseek-reasoner)")
        print("2. Ollama (local models)")
        
        while True:
            choice = input("\nSelect provider (1-2): ").strip()
            if choice == "1":
                provider = "deepseek"
                break
            elif choice == "2":
                provider = "ollama"
                break
            else:
                print("Invalid choice. Please select 1 or 2.")
        
        config = self.load_config()
        config.llm.provider = provider
        
        if provider == "deepseek":
            # Deepseek setup
            api_key = input("\nEnter Deepseek API key: ").strip()
            if not api_key:
                print("API key is required for Deepseek!")
                return None
            
            print("\nAvailable Deepseek models:")
            print("1. deepseek-chat (default)")
            print("2. deepseek-reasoner")
            
            model_choice = input("Select model (1-2, default: 1): ").strip() or "1"
            model = "deepseek-chat" if model_choice == "1" else "deepseek-reasoner"
            
            config.llm.api_key = api_key
            config.llm.model = model
            config.llm.base_url = "https://api.deepseek.com"
            
        elif provider == "ollama":
            # Ollama setup
            base_url = input("\nEnter Ollama base URL (default: http://localhost:11434): ").strip()
            if not base_url:
                base_url = "http://localhost:11434"
            
            model = input("Enter model name (e.g., llama2, codellama): ").strip()
            if not model:
                print("Model name is required for Ollama!")
                return None
            
            config.llm.base_url = base_url
            config.llm.model = model
            config.llm.api_key = None
        
        # Save configuration
        if self.save_config(config):
            print(f"\n✅ Configuration saved successfully!")
            print(f"Provider: {config.llm.provider}")
            print(f"Model: {config.llm.model}")
            return config
        else:
            print("\n❌ Failed to save configuration!")
            return None
    
    def validate_config(self, config: ArienConfig) -> bool:
        """Validate configuration"""
        if config.llm.provider == "deepseek":
            if not config.llm.api_key:
                print("❌ Deepseek API key is required!")
                return False
        elif config.llm.provider == "ollama":
            if not config.llm.base_url or not config.llm.model:
                print("❌ Ollama base URL and model are required!")
                return False
        else:
            print(f"❌ Unknown provider: {config.llm.provider}")
            return False
        
        return True


# Global config manager instance
config_manager = ConfigManager()
