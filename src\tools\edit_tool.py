"""
Edit Tool - File modification and text replacement
"""
import os
import re
import shutil
from typing import Dict, Any, Optional, List
from pathlib import Path
import tempfile


class EditTool:
    """File editing tool for targeted modifications"""
    
    def __init__(self, backup_enabled: bool = True):
        self.backup_enabled = backup_enabled
        self.backup_dir = Path.home() / ".arien-ai" / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    async def edit_file(self, filepath: str, old_text: str, new_text: str,
                       count: int = -1, regex: bool = False, 
                       case_sensitive: bool = True, backup: Optional[bool] = None) -> Dict[str, Any]:
        """
        Edit file by replacing text
        
        Args:
            filepath: Path to the file to edit
            old_text: Text to replace (or regex pattern if regex=True)
            new_text: Replacement text
            count: Maximum number of replacements (-1 for all)
            regex: Whether old_text is a regex pattern
            case_sensitive: Whether replacement is case sensitive
            backup: Create backup before editing
            
        Returns:
            Dict with edit operation results
        """
        try:
            file_path = Path(filepath).resolve()
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File not found: {filepath}",
                    "filepath": str(file_path)
                }
            
            if not file_path.is_file():
                return {
                    "success": False,
                    "error": f"Path is not a file: {filepath}",
                    "filepath": str(file_path)
                }
            
            # Read current content
            try:
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    original_content = f.read()
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Failed to read file: {str(e)}",
                    "filepath": str(file_path)
                }
            
            # Perform replacement
            if regex:
                # Regex replacement
                flags = 0 if case_sensitive else re.IGNORECASE
                try:
                    pattern = re.compile(old_text, flags)
                    new_content, replacements = pattern.subn(new_text, original_content, count=count)
                except re.error as e:
                    return {
                        "success": False,
                        "error": f"Invalid regex pattern: {str(e)}",
                        "filepath": str(file_path)
                    }
            else:
                # String replacement
                if case_sensitive:
                    if count == -1:
                        new_content = original_content.replace(old_text, new_text)
                        replacements = original_content.count(old_text)
                    else:
                        new_content = original_content.replace(old_text, new_text, count)
                        replacements = min(original_content.count(old_text), count)
                else:
                    # Case insensitive string replacement
                    pattern = re.compile(re.escape(old_text), re.IGNORECASE)
                    new_content, replacements = pattern.subn(new_text, original_content, count=count)
            
            # Check if any changes were made
            if replacements == 0:
                return {
                    "success": False,
                    "error": "No matches found for replacement",
                    "filepath": str(file_path),
                    "replacements": 0
                }
            
            # Create backup if requested
            backup_path = None
            should_backup = backup if backup is not None else self.backup_enabled
            if should_backup:
                backup_path = await self._create_backup(file_path)
            
            # Write modified content
            try:
                with tempfile.NamedTemporaryFile(
                    mode='w', 
                    encoding='utf-8', 
                    delete=False,
                    dir=file_path.parent
                ) as tf:
                    tf.write(new_content)
                    temp_file = tf.name
                
                # Atomic move to final location
                shutil.move(temp_file, str(file_path))
                
                # Get file info
                stat_info = file_path.stat()
                
                return {
                    "success": True,
                    "filepath": str(file_path),
                    "replacements": replacements,
                    "old_size": len(original_content),
                    "new_size": len(new_content),
                    "size_change": len(new_content) - len(original_content),
                    "backup_created": backup_path is not None,
                    "backup_path": backup_path,
                    "regex_used": regex,
                    "case_sensitive": case_sensitive
                }
                
            except Exception as e:
                # Clean up temp file on error
                if 'temp_file' in locals() and os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except:
                        pass
                raise e
                
        except PermissionError:
            return {
                "success": False,
                "error": f"Permission denied: {filepath}",
                "filepath": filepath
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Edit error: {str(e)}",
                "filepath": filepath
            }
    
    async def insert_text(self, filepath: str, text: str, line_number: int = -1,
                         position: str = "end") -> Dict[str, Any]:
        """
        Insert text into file at specific location
        
        Args:
            filepath: Path to the file
            text: Text to insert
            line_number: Line number to insert at (-1 for end)
            position: Where to insert ("start", "end", "before", "after")
        """
        try:
            file_path = Path(filepath).resolve()
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File not found: {filepath}",
                    "filepath": str(file_path)
                }
            
            # Read current content
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                lines = f.readlines()
            
            # Insert text
            if position == "start":
                lines.insert(0, text + '\n')
            elif position == "end":
                lines.append(text + '\n')
            elif position == "before" and 0 <= line_number <= len(lines):
                lines.insert(line_number - 1, text + '\n')
            elif position == "after" and 0 <= line_number <= len(lines):
                lines.insert(line_number, text + '\n')
            else:
                return {
                    "success": False,
                    "error": f"Invalid position or line number: {position}, {line_number}",
                    "filepath": str(file_path)
                }
            
            # Write modified content
            new_content = ''.join(lines)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return {
                "success": True,
                "filepath": str(file_path),
                "inserted_text": text,
                "position": position,
                "line_number": line_number,
                "new_line_count": len(lines)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Insert error: {str(e)}",
                "filepath": filepath
            }
    
    async def _create_backup(self, file_path: Path) -> Optional[str]:
        """Create backup of existing file"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path.name}.{timestamp}.backup"
            backup_path = self.backup_dir / backup_name
            
            shutil.copy2(str(file_path), str(backup_path))
            return str(backup_path)
        except Exception:
            return None


# Tool function for AI agent
async def edit(filepath: str, old_text: str, new_text: str, 
               regex: bool = False, case_sensitive: bool = True) -> Dict[str, Any]:
    """
    Edit existing files by replacing text
    
    Usage Examples:
    - edit("/etc/config", "debug=false", "debug=true")
    - edit("app.py", "old_function()", "new_function()")
    - edit("README.md", "# Old Title", "# New Title")
    - edit("config.py", r"PORT\\s*=\\s*\\d+", "PORT = 8080", regex=True)
    
    Args:
        filepath: Path to the file to edit
        old_text: Text to replace (or regex pattern if regex=True)
        new_text: Replacement text
        regex: Whether old_text is a regex pattern
        case_sensitive: Whether replacement should be case sensitive
    
    Returns:
        Dictionary with edit operation results
    """
    tool = EditTool()
    result = await tool.edit_file(filepath, old_text, new_text, 
                                 regex=regex, case_sensitive=case_sensitive)
    
    # Format output for AI agent
    if result["success"]:
        output = f"✅ File edited successfully\n"
        output += f"Path: {result['filepath']}\n"
        output += f"Replacements made: {result['replacements']}\n"
        output += f"Size change: {result['size_change']:+d} characters\n"
        output += f"Method: {'Regex' if result['regex_used'] else 'String'} replacement\n"
        output += f"Case sensitive: {result['case_sensitive']}\n"
        
        if result["backup_created"]:
            output += f"Backup created: {result['backup_path']}\n"
    else:
        output = f"❌ Failed to edit file\n"
        output += f"Path: {result['filepath']}\n"
        output += f"Error: {result['error']}\n"
        
        if "replacements" in result:
            output += f"Replacements found: {result['replacements']}\n"
    
    return {
        "tool": "edit",
        "success": result["success"],
        "output": output,
        "raw_result": result
    }
