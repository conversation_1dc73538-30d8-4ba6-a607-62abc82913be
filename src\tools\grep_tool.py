"""
Grep Tool - Fast content search within files
"""
import re
import os
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
import glob as glob_module


class GrepTool:
    """Fast content search tool using regex patterns"""
    
    def __init__(self):
        self.max_file_size = 100 * 1024 * 1024  # 100MB limit
        self.max_results = 1000
    
    async def search(self, pattern: str, files: str, case_sensitive: bool = False, 
                    context_lines: int = 0, max_results: Optional[int] = None) -> Dict[str, Any]:
        """
        Search for pattern in files
        
        Args:
            pattern: Regex pattern to search for
            files: File paths or glob patterns
            case_sensitive: Whether search is case sensitive
            context_lines: Number of context lines to include
            max_results: Maximum number of results to return
            
        Returns:
            Dict with search results
        """
        try:
            # Compile regex pattern
            flags = 0 if case_sensitive else re.IGNORECASE
            regex = re.compile(pattern, flags)
            
            # Expand file patterns
            file_list = self._expand_file_patterns(files)
            
            if not file_list:
                return {
                    "success": False,
                    "error": f"No files found matching pattern: {files}",
                    "matches": []
                }
            
            # Search in files
            all_matches = []
            max_res = max_results or self.max_results
            
            for file_path in file_list:
                if len(all_matches) >= max_res:
                    break
                
                matches = await self._search_in_file(
                    file_path, regex, context_lines, max_res - len(all_matches)
                )
                all_matches.extend(matches)
            
            # Sort by modification time (newest first)
            all_matches.sort(key=lambda x: x.get('mtime', 0), reverse=True)
            
            return {
                "success": True,
                "pattern": pattern,
                "files_searched": len(file_list),
                "total_matches": len(all_matches),
                "matches": all_matches[:max_res]
            }
            
        except re.error as e:
            return {
                "success": False,
                "error": f"Invalid regex pattern: {str(e)}",
                "matches": []
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Search error: {str(e)}",
                "matches": []
            }
    
    def _expand_file_patterns(self, files: str) -> List[str]:
        """Expand file patterns to actual file paths"""
        file_list = []
        
        # Split multiple patterns
        patterns = [p.strip() for p in files.split(',')]
        
        for pattern in patterns:
            if os.path.isfile(pattern):
                # Direct file path
                file_list.append(pattern)
            else:
                # Glob pattern
                matches = glob_module.glob(pattern, recursive=True)
                for match in matches:
                    if os.path.isfile(match):
                        file_list.append(match)
        
        # Remove duplicates and sort by modification time
        unique_files = list(set(file_list))
        unique_files.sort(key=lambda f: os.path.getmtime(f) if os.path.exists(f) else 0, reverse=True)
        
        return unique_files
    
    async def _search_in_file(self, file_path: str, regex: re.Pattern, 
                             context_lines: int, max_matches: int) -> List[Dict[str, Any]]:
        """Search for pattern in a single file"""
        matches = []
        
        try:
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                return [{
                    "file": file_path,
                    "line_number": 0,
                    "line": f"File too large ({file_size} bytes), skipped",
                    "match": "",
                    "context_before": [],
                    "context_after": [],
                    "mtime": os.path.getmtime(file_path)
                }]
            
            # Read file content
            try:
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    lines = f.readlines()
            except UnicodeDecodeError:
                # Try binary mode for non-text files
                with open(file_path, 'rb') as f:
                    content = f.read()
                    lines = content.decode('utf-8', errors='replace').splitlines(True)
            
            # Search for matches
            for line_num, line in enumerate(lines, 1):
                if len(matches) >= max_matches:
                    break
                
                match = regex.search(line)
                if match:
                    # Get context lines
                    context_before = []
                    context_after = []
                    
                    if context_lines > 0:
                        start_idx = max(0, line_num - context_lines - 1)
                        end_idx = min(len(lines), line_num + context_lines)
                        
                        context_before = [
                            lines[i].rstrip('\n\r') 
                            for i in range(start_idx, line_num - 1)
                        ]
                        context_after = [
                            lines[i].rstrip('\n\r') 
                            for i in range(line_num, end_idx)
                        ]
                    
                    matches.append({
                        "file": file_path,
                        "line_number": line_num,
                        "line": line.rstrip('\n\r'),
                        "match": match.group(0),
                        "match_start": match.start(),
                        "match_end": match.end(),
                        "context_before": context_before,
                        "context_after": context_after,
                        "mtime": os.path.getmtime(file_path)
                    })
            
        except Exception as e:
            matches.append({
                "file": file_path,
                "line_number": 0,
                "line": f"Error reading file: {str(e)}",
                "match": "",
                "context_before": [],
                "context_after": [],
                "mtime": 0
            })
        
        return matches


# Tool function for AI agent
async def grep(pattern: str, files: str, case_sensitive: bool = False, 
               context: int = 0) -> Dict[str, Any]:
    """
    Search for text patterns within files
    
    Usage Examples:
    - grep("error", "/var/log/*.log")
    - grep("def main", "*.py")
    - grep("TODO", "/project/**/*.py")
    - grep("import.*requests", "*.py", case_sensitive=True)
    
    Args:
        pattern: Regex pattern to search for
        files: File paths or glob patterns (comma-separated)
        case_sensitive: Whether search should be case sensitive
        context: Number of context lines to show around matches
    
    Returns:
        Dictionary with search results
    """
    tool = GrepTool()
    result = await tool.search(pattern, files, case_sensitive, context)
    
    # Format output for AI agent
    if result["success"]:
        output = f"🔍 Search completed successfully\n"
        output += f"Pattern: {result['pattern']}\n"
        output += f"Files searched: {result['files_searched']}\n"
        output += f"Total matches: {result['total_matches']}\n\n"
        
        if result["matches"]:
            output += "Matches (sorted by modification time):\n"
            for i, match in enumerate(result["matches"][:20], 1):  # Show first 20
                output += f"\n{i}. {match['file']}:{match['line_number']}\n"
                
                # Show context before
                for ctx_line in match["context_before"]:
                    output += f"   | {ctx_line}\n"
                
                # Show matching line with highlight
                line = match["line"]
                if match["match"]:
                    start, end = match["match_start"], match["match_end"]
                    highlighted = line[:start] + f">>>{match['match']}<<<" + line[end:]
                    output += f" > | {highlighted}\n"
                else:
                    output += f" > | {line}\n"
                
                # Show context after
                for ctx_line in match["context_after"]:
                    output += f"   | {ctx_line}\n"
            
            if len(result["matches"]) > 20:
                output += f"\n... and {len(result['matches']) - 20} more matches\n"
        else:
            output += "No matches found.\n"
    else:
        output = f"❌ Search failed\n"
        output += f"Error: {result['error']}\n"
    
    return {
        "tool": "grep",
        "success": result["success"],
        "output": output,
        "raw_result": result
    }
