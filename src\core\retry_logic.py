"""
Never Give Up Retry Logic - Intelligent retry system with exponential backoff
"""
import asyncio
import time
import random
from typing import Any, Callable, Dict, List, Optional, Type, Union
from dataclasses import dataclass
from enum import Enum


class ErrorType(Enum):
    """Classification of error types for retry decisions"""
    TEMPORARY = "temporary"      # Network timeouts, rate limits
    PERMANENT = "permanent"      # Auth failures, file not found
    UNKNOWN = "unknown"         # Unclassified errors


@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_attempts: int = 5
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    backoff_factor: float = 1.0


@dataclass
class RetryAttempt:
    """Information about a retry attempt"""
    attempt_number: int
    error: Exception
    delay: float
    timestamp: float
    error_type: ErrorType


class RetryableError(Exception):
    """Base class for retryable errors"""
    def __init__(self, message: str, error_type: ErrorType = ErrorType.TEMPORARY):
        super().__init__(message)
        self.error_type = error_type


class RetryLogic:
    """Intelligent retry system with exponential backoff"""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        self.config = config or RetryConfig()
        self.attempt_history: List[RetryAttempt] = []
    
    def classify_error(self, error: Exception) -> ErrorType:
        """Classify error type to determine retry strategy"""
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        # Temporary errors (worth retrying)
        temporary_indicators = [
            "timeout", "connection", "network", "rate limit", "throttle",
            "503", "502", "504", "429", "temporary", "busy", "overload"
        ]
        
        # Permanent errors (don't retry)
        permanent_indicators = [
            "401", "403", "404", "permission denied", "unauthorized",
            "authentication", "invalid api key", "not found", "syntax error",
            "invalid argument", "file not found"
        ]
        
        # Check for RetryableError with explicit type
        if isinstance(error, RetryableError):
            return error.error_type
        
        # Check for temporary error indicators
        for indicator in temporary_indicators:
            if indicator in error_str or indicator in error_type:
                return ErrorType.TEMPORARY
        
        # Check for permanent error indicators
        for indicator in permanent_indicators:
            if indicator in error_str or indicator in error_type:
                return ErrorType.PERMANENT
        
        # Default to unknown (will retry with caution)
        return ErrorType.UNKNOWN
    
    def calculate_delay(self, attempt: int, error_type: ErrorType) -> float:
        """Calculate delay before next retry attempt"""
        if error_type == ErrorType.PERMANENT:
            return 0  # Don't retry permanent errors
        
        # Base exponential backoff
        delay = self.config.base_delay * (
            self.config.exponential_base ** (attempt - 1)
        ) * self.config.backoff_factor
        
        # Apply jitter to avoid thundering herd
        if self.config.jitter:
            jitter_factor = 0.1 * random.random()  # 0-10% jitter
            delay *= (1 + jitter_factor)
        
        # Cap at maximum delay
        delay = min(delay, self.config.max_delay)
        
        # Special handling for rate limits
        if "rate limit" in str(self.attempt_history[-1].error).lower():
            delay = max(delay, 5.0)  # Minimum 5 seconds for rate limits
        
        return delay
    
    async def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with intelligent retry logic
        
        Args:
            func: Function to execute (can be async or sync)
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: Final exception if all retries exhausted
        """
        self.attempt_history.clear()
        last_error = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                # Execute function (handle both async and sync)
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Success! Return result
                return result
                
            except Exception as error:
                last_error = error
                error_type = self.classify_error(error)
                
                # Record attempt
                retry_attempt = RetryAttempt(
                    attempt_number=attempt,
                    error=error,
                    delay=0,
                    timestamp=time.time(),
                    error_type=error_type
                )
                self.attempt_history.append(retry_attempt)
                
                # Don't retry permanent errors
                if error_type == ErrorType.PERMANENT:
                    raise error
                
                # Don't retry on last attempt
                if attempt >= self.config.max_attempts:
                    break
                
                # Calculate delay for next attempt
                delay = self.calculate_delay(attempt, error_type)
                retry_attempt.delay = delay
                
                if delay > 0:
                    await asyncio.sleep(delay)
        
        # All retries exhausted
        raise last_error
    
    def get_retry_summary(self) -> Dict[str, Any]:
        """Get summary of retry attempts"""
        if not self.attempt_history:
            return {"attempts": 0, "total_time": 0}
        
        total_time = sum(attempt.delay for attempt in self.attempt_history)
        error_types = [attempt.error_type.value for attempt in self.attempt_history]
        
        return {
            "attempts": len(self.attempt_history),
            "total_time": total_time,
            "error_types": error_types,
            "final_error": str(self.attempt_history[-1].error) if self.attempt_history else None,
            "success": False
        }


class RetryableOperation:
    """Decorator and context manager for retryable operations"""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        self.retry_logic = RetryLogic(config)
    
    def __call__(self, func: Callable) -> Callable:
        """Decorator usage"""
        async def wrapper(*args, **kwargs):
            return await self.retry_logic.execute_with_retry(func, *args, **kwargs)
        return wrapper
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self.retry_logic
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        pass


# Predefined retry configurations
AGGRESSIVE_RETRY = RetryConfig(
    max_attempts=10,
    base_delay=0.5,
    max_delay=30.0,
    exponential_base=1.5
)

CONSERVATIVE_RETRY = RetryConfig(
    max_attempts=3,
    base_delay=2.0,
    max_delay=10.0,
    exponential_base=2.0
)

NETWORK_RETRY = RetryConfig(
    max_attempts=5,
    base_delay=1.0,
    max_delay=60.0,
    exponential_base=2.0,
    backoff_factor=1.5
)


# Convenience functions
async def retry_on_failure(func: Callable, *args, config: Optional[RetryConfig] = None, **kwargs) -> Any:
    """Convenience function to retry any operation"""
    retry_logic = RetryLogic(config)
    return await retry_logic.execute_with_retry(func, *args, **kwargs)


def retryable(config: Optional[RetryConfig] = None):
    """Decorator to make any function retryable"""
    return RetryableOperation(config)


# Global retry instance
default_retry = RetryLogic()
