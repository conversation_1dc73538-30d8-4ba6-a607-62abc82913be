"""
Main CLI Interface - Interactive terminal interface with real-time updates
"""
import asyncio
import sys
from typing import Optional
from rich.console import Console
from rich.prompt import Prompt
from rich.text import Text
from rich.panel import Panel
from rich.live import Live
from prompt_toolkit import PromptSession
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory

from ..core.agent import ArienAgent
from ..config.settings import config_manager, ArienConfig
from .animations import (
    BallAnimation, ProgressIndicator, StreamingDisplay, 
    WelcomeScreen, console
)


class ArienCLI:
    """Main CLI interface for Arien AI"""
    
    def __init__(self):
        self.console = console
        self.agent: Optional[ArienAgent] = None
        self.config: Optional[ArienConfig] = None
        self.welcome_screen = WelcomeScreen(self.console)
        self.progress = ProgressIndicator(self.console)
        self.streaming = StreamingDisplay(self.console)
        self.ball_animation = BallAnimation(self.console)
        
        # Prompt session for better input handling
        self.prompt_session = PromptSession(
            history=InMemoryHistory(),
            auto_suggest=AutoSuggestFromHistory(),
        )
    
    async def start(self):
        """Start the CLI interface"""
        try:
            # Load or setup configuration
            await self._initialize_config()
            
            if not self.config:
                self.console.print("❌ Failed to initialize configuration. Exiting.")
                return
            
            # Initialize agent
            self.agent = ArienAgent(self.config)
            
            # Show welcome screen
            self.welcome_screen.show_welcome()
            
            # Start interactive loop
            await self._interactive_loop()
            
        except KeyboardInterrupt:
            self.console.print("\n👋 Goodbye!")
        except Exception as e:
            self.console.print(f"❌ Fatal error: {e}")
    
    async def _initialize_config(self):
        """Initialize configuration"""
        self.config = config_manager.load_config()
        
        # Check if configuration is valid
        if not config_manager.validate_config(self.config):
            self.welcome_screen.show_setup_required()
            
            # Run setup
            setup_choice = Prompt.ask(
                "\n[yellow]Run setup now?[/yellow]", 
                choices=["y", "n"], 
                default="y"
            )
            
            if setup_choice.lower() == "y":
                self.config = config_manager.setup_llm_provider()
                if not self.config:
                    return
            else:
                self.console.print("Setup cancelled. Cannot proceed without configuration.")
                return
    
    async def _interactive_loop(self):
        """Main interactive loop"""
        self.console.print("[dim]Type 'help' for commands, 'exit' to quit[/dim]\n")
        
        while True:
            try:
                # Get user input
                user_input = await self._get_user_input()
                
                if not user_input.strip():
                    continue
                
                # Handle special commands
                if await self._handle_special_commands(user_input):
                    continue
                
                # Process with AI agent
                await self._process_user_message(user_input)
                
            except KeyboardInterrupt:
                self.console.print("\n[dim]Use 'exit' to quit[/dim]")
                continue
            except EOFError:
                break
    
    async def _get_user_input(self) -> str:
        """Get user input with prompt"""
        try:
            # Create prompt text
            prompt_text = Text()
            prompt_text.append("🤖 ", style="cyan bold")
            prompt_text.append("You: ", style="white bold")
            
            # Use prompt_toolkit for better input experience
            user_input = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: self.prompt_session.prompt(
                    message=prompt_text,
                    multiline=False
                )
            )
            
            return user_input
            
        except Exception:
            # Fallback to simple input
            return input("🤖 You: ")
    
    async def _handle_special_commands(self, user_input: str) -> bool:
        """Handle special CLI commands"""
        command = user_input.strip().lower()
        
        if command in ["exit", "quit", "bye"]:
            self.console.print("👋 Goodbye!")
            sys.exit(0)
        
        elif command == "help":
            self._show_help()
            return True
        
        elif command == "clear":
            self.console.clear()
            self.welcome_screen.show_welcome()
            return True
        
        elif command == "config":
            await self._show_config()
            return True
        
        elif command == "setup":
            await self._run_setup()
            return True
        
        elif command == "history":
            self._show_conversation_history()
            return True
        
        elif command == "reset":
            if self.agent:
                self.agent.clear_conversation()
                self.console.print("🔄 Conversation history cleared.")
            return True
        
        return False
    
    def _show_help(self):
        """Show help information"""
        help_text = """
[bold cyan]Arien AI Commands:[/bold cyan]

[yellow]Special Commands:[/yellow]
• [green]help[/green]     - Show this help message
• [green]clear[/green]    - Clear screen
• [green]config[/green]   - Show current configuration
• [green]setup[/green]    - Run configuration setup
• [green]history[/green]  - Show conversation history
• [green]reset[/green]    - Clear conversation history
• [green]exit[/green]     - Exit Arien AI

[yellow]Available Tools:[/yellow]
• [green]bash[/green]     - Execute shell commands
• [green]grep[/green]     - Search file contents
• [green]glob[/green]     - Find files by pattern
• [green]write[/green]    - Create/update files
• [green]edit[/green]     - Modify existing files
• [green]web[/green]      - Search the internet

[blue]Just type your request and AI will handle the rest![/blue]
        """
        
        panel = Panel(
            help_text,
            title="[bold]Help[/bold]",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    async def _show_config(self):
        """Show current configuration"""
        if not self.config:
            self.console.print("❌ No configuration loaded")
            return
        
        config_text = f"""
[yellow]LLM Provider:[/yellow] {self.config.llm.provider}
[yellow]Model:[/yellow] {self.config.llm.model}
[yellow]Base URL:[/yellow] {self.config.llm.base_url or 'Default'}
[yellow]Debug Mode:[/yellow] {self.config.debug}
[yellow]Log Level:[/yellow] {self.config.log_level}

[yellow]UI Settings:[/yellow]
• Animation Speed: {self.config.ui.animation_speed}
• Show Progress: {self.config.ui.show_progress}
• Color Scheme: {self.config.ui.color_scheme}

[yellow]Tool Settings:[/yellow]
• Bash Timeout: {self.config.tools.bash_timeout}s
• Web Results: {self.config.tools.web_search_results}
• File Backup: {self.config.tools.file_backup}
• Parallel Execution: {self.config.tools.parallel_execution}
        """
        
        panel = Panel(
            config_text,
            title="[bold]Current Configuration[/bold]",
            border_style="yellow",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    async def _run_setup(self):
        """Run configuration setup"""
        self.console.print("🔧 Running configuration setup...\n")
        
        new_config = config_manager.setup_llm_provider()
        if new_config:
            self.config = new_config
            self.agent = ArienAgent(self.config)
            self.console.print("✅ Configuration updated successfully!")
        else:
            self.console.print("❌ Setup failed or cancelled.")
    
    def _show_conversation_history(self):
        """Show conversation history summary"""
        if not self.agent:
            self.console.print("❌ No agent initialized")
            return
        
        summary = self.agent.get_conversation_summary()
        
        history_text = f"""
[yellow]Messages:[/yellow] {summary['message_count']}
[yellow]Total Length:[/yellow] {summary['conversation_length']} characters

[yellow]Last Message:[/yellow]
{summary['last_message']['content'][:200] + '...' if summary['last_message'] and len(summary['last_message']['content']) > 200 else summary['last_message']['content'] if summary['last_message'] else 'None'}
        """
        
        panel = Panel(
            history_text,
            title="[bold]Conversation History[/bold]",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    async def _process_user_message(self, user_input: str):
        """Process user message with AI agent"""
        if not self.agent:
            self.console.print("❌ Agent not initialized")
            return
        
        try:
            # Show thinking animation
            self.ball_animation.start()
            
            # Stream AI response
            response_text = ""
            
            with Live(console=self.console, refresh_per_second=10) as live:
                async for chunk in self.agent.stream_response(user_input):
                    response_text += chunk
                    
                    # Update display
                    display_text = Text()
                    display_text.append("🤖 Arien: ", style="cyan bold")
                    display_text.append(response_text, style="white")
                    
                    live.update(display_text)
                    await asyncio.sleep(0.01)
            
            self.ball_animation.stop()
            self.console.print()  # Add spacing
            
        except Exception as e:
            self.ball_animation.stop()
            self.console.print(f"\n❌ Error processing message: {e}")


# Main CLI instance
cli = ArienCLI()
