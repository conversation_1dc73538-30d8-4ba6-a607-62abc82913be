# 🤖 Arien AI - Advanced Terminal Assistant

A powerful AI-powered CLI terminal system with intelligent function calling capabilities, never-give-up retry logic, and real-time streaming responses.

## ✨ Features

### 🧠 AI-Powered Intelligence
- **LLM Providers**: Deepseek (deepseek-chat, deepseek-reasoner) and Ollama support
- **Function Tools**: 6 specialized tools with intelligent parallel/sequential execution
- **Smart Decision Making**: AI automatically decides when and how to use tools
- **Never Give Up**: Intelligent retry logic with exponential backoff

### 🛠️ Function Tools

| Tool | Purpose | Usage Examples |
|------|---------|----------------|
| **bash** | Execute shell commands | `bash("ls -la")`, `bash("ps aux \| grep python")` |
| **grep** | Search file contents | `grep("error", "/var/log/*.log")`, `grep("TODO", "*.py")` |
| **glob** | Find files by pattern | `glob("*.py")`, `glob("config.*", "/etc")` |
| **write** | Create/update files | `write("/tmp/script.py", "print('Hello')")` |
| **edit** | Modify existing files | `edit("config.py", "debug=False", "debug=True")` |
| **web** | Search the internet | `web("Python 3.12 installation guide")` |

### 🎨 User Experience
- **Real-time Streaming**: See AI responses as they're generated
- **Ball Animation**: Custom progress indicator with elapsed time
- **Rich Interface**: Beautiful CLI with colors, panels, and formatting
- **Interactive Setup**: Easy configuration wizard for LLM providers
- **Error Handling**: Comprehensive error management with clear feedback

### 🔄 Retry Logic
- **Intelligent Classification**: Distinguishes temporary vs permanent errors
- **Exponential Backoff**: Smart delay calculation with jitter
- **Rate Limit Handling**: Special handling for API rate limits
- **Never Give Up**: Persistent execution until task completion

## 🚀 Quick Start

### Installation

#### Option 1: Universal Installer (Recommended)
```bash
# Download and run installer
python install.py install
```

#### Option 2: Manual Installation
```bash
# Clone repository
git clone <repository-url>
cd arien-ai-cli

# Install dependencies
pip install -r requirements.txt

# Run directly
python main.py
```

### First Run Setup

1. **Start Arien AI**:
   ```bash
   arien-ai
   ```

2. **Configure LLM Provider**:
   - Choose between Deepseek or Ollama
   - Enter API key (for Deepseek) or model name (for Ollama)
   - Configuration is saved automatically

3. **Start Using**:
   ```
   🤖 You: Find all Python files in the current directory and search for TODO comments
   ```

## 📋 System Requirements

- **Python**: 3.8 or higher
- **Operating Systems**: 
  - Windows 11 (with WSL)
  - macOS
  - Linux
- **Memory**: 512MB RAM minimum
- **Storage**: 100MB free space

## 🔧 Configuration

### LLM Providers

#### Deepseek Setup
```yaml
llm:
  provider: deepseek
  api_key: "your-api-key-here"
  model: "deepseek-chat"  # or "deepseek-reasoner"
  base_url: "https://api.deepseek.com"
```

#### Ollama Setup
```yaml
llm:
  provider: ollama
  model: "llama2"  # or any installed model
  base_url: "http://localhost:11434"
```

### Advanced Configuration
```yaml
ui:
  animation_speed: 0.1
  show_progress: true
  color_scheme: "default"

tools:
  bash_timeout: 300
  web_search_results: 5
  file_backup: true
  parallel_execution: true
```

## 🎯 Usage Examples

### Basic Commands
```bash
# Get help
help

# Clear screen
clear

# Show configuration
config

# Reset conversation
reset

# Exit
exit
```

### AI Interactions

#### File Operations
```
🤖 You: Create a Python script that prints "Hello World" and make it executable
```

#### System Administration
```
🤖 You: Check system memory usage and find the top 5 processes consuming most RAM
```

#### Development Tasks
```
🤖 You: Find all JavaScript files with console.log statements and replace them with proper logging
```

#### Research and Documentation
```
🤖 You: Search for the latest Docker best practices and create a summary document
```

## 🏗️ Architecture

### Project Structure
```
arien-ai-cli/
├── src/
│   ├── core/           # AI agent and LLM providers
│   ├── tools/          # Function tools implementation
│   ├── ui/             # CLI interface and animations
│   ├── config/         # Configuration management
│   └── utils/          # Utilities and error handling
├── main.py             # Entry point
├── install.py          # Universal installer
└── requirements.txt    # Dependencies
```

### Core Components

1. **AI Agent** (`src/core/agent.py`)
   - Main intelligence with function calling
   - Conversation management
   - Streaming response handling

2. **LLM Providers** (`src/core/llm_providers.py`)
   - Deepseek and Ollama integration
   - Unified API interface
   - Error handling and retries

3. **Tool Executor** (`src/core/tool_executor.py`)
   - Parallel/sequential execution
   - Tool result processing
   - Dependency management

4. **Retry Logic** (`src/core/retry_logic.py`)
   - Intelligent error classification
   - Exponential backoff
   - Never-give-up persistence

## 🔧 Development

### Adding New Tools

1. Create tool file in `src/tools/`:
```python
async def my_tool(param1: str, param2: int = 10) -> Dict[str, Any]:
    """Tool description"""
    # Implementation
    return {
        "tool": "my_tool",
        "success": True,
        "output": "Tool output",
        "raw_result": {}
    }
```

2. Register in `src/core/tool_executor.py`:
```python
from ..tools.my_tool import my_tool

self.tools = {
    # ... existing tools
    "my_tool": my_tool
}
```

3. Add description in `src/config/prompts.py`:
```python
TOOL_DESCRIPTIONS = {
    # ... existing tools
    "my_tool": {
        "description": "Tool description",
        "parameters": {"param1": "Parameter description"},
        "examples": ["my_tool('example')"]
    }
}
```

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/
```

## 📦 Installation Management

### Install Globally
```bash
python install.py install
```

### Update Installation
```bash
python install.py update
```

### Uninstall
```bash
python install.py uninstall
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Issues**: Report bugs and request features on GitHub
- **Documentation**: Check the wiki for detailed guides
- **Community**: Join our Discord server for discussions

## 🙏 Acknowledgments

- **Deepseek**: For providing powerful AI models
- **Ollama**: For local AI model support
- **Rich**: For beautiful terminal interfaces
- **DuckDuckGo**: For web search capabilities

---

**Made with ❤️ by the Arien AI team**
