"""
Web Tool - Internet information retrieval using DuckDuckGo
"""
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from duckduckgo_search import DDGS
import json
import re


class WebTool:
    """Web search tool using DuckDuckGo for real-time information"""
    
    def __init__(self):
        self.max_results = 10
        self.timeout = 30
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    
    async def search(self, query: str, max_results: Optional[int] = None,
                    region: str = "us-en", safesearch: str = "moderate") -> Dict[str, Any]:
        """
        Search the web for information
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return
            region: Search region (default: us-en)
            safesearch: Safe search setting (strict, moderate, off)
            
        Returns:
            Dict with search results and metadata
        """
        try:
            max_res = max_results or self.max_results
            
            # Perform search using DuckDuckGo
            with DDGS() as ddgs:
                results = []
                search_results = ddgs.text(
                    query, 
                    region=region, 
                    safesearch=safesearch, 
                    max_results=max_res
                )
                
                for result in search_results:
                    results.append({
                        "title": result.get("title", ""),
                        "url": result.get("href", ""),
                        "snippet": result.get("body", ""),
                        "source": self._extract_domain(result.get("href", ""))
                    })
            
            return {
                "success": True,
                "query": query,
                "total_results": len(results),
                "results": results,
                "region": region,
                "safesearch": safesearch
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Search error: {str(e)}",
                "query": query,
                "results": []
            }
    
    async def get_page_content(self, url: str, max_length: int = 5000) -> Dict[str, Any]:
        """
        Fetch and extract text content from a webpage
        
        Args:
            url: URL to fetch
            max_length: Maximum content length to return
            
        Returns:
            Dict with page content and metadata
        """
        try:
            headers = {
                "User-Agent": self.user_agent,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, 
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        # Extract text content (basic HTML stripping)
                        text_content = self._extract_text_from_html(content)
                        
                        # Limit content length
                        if len(text_content) > max_length:
                            text_content = text_content[:max_length] + "..."
                        
                        return {
                            "success": True,
                            "url": url,
                            "status_code": response.status,
                            "content": text_content,
                            "content_length": len(text_content),
                            "title": self._extract_title_from_html(content)
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}",
                            "url": url,
                            "status_code": response.status
                        }
                        
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": "Request timeout",
                "url": url
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Fetch error: {str(e)}",
                "url": url
            }
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            import urllib.parse
            parsed = urllib.parse.urlparse(url)
            return parsed.netloc
        except:
            return "unknown"
    
    def _extract_text_from_html(self, html: str) -> str:
        """Extract text content from HTML (basic implementation)"""
        try:
            # Remove script and style elements
            html = re.sub(r'<script[^>]*>.*?</script>', '', html, flags=re.DOTALL | re.IGNORECASE)
            html = re.sub(r'<style[^>]*>.*?</style>', '', html, flags=re.DOTALL | re.IGNORECASE)
            
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', html)
            
            # Clean up whitespace
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
            
            return text
        except:
            return html
    
    def _extract_title_from_html(self, html: str) -> str:
        """Extract title from HTML"""
        try:
            title_match = re.search(r'<title[^>]*>(.*?)</title>', html, re.IGNORECASE | re.DOTALL)
            if title_match:
                title = title_match.group(1)
                title = re.sub(r'<[^>]+>', '', title)  # Remove any tags
                title = re.sub(r'\s+', ' ', title).strip()
                return title
            return "No title"
        except:
            return "No title"
    
    async def search_news(self, query: str, max_results: Optional[int] = None) -> Dict[str, Any]:
        """
        Search for news articles
        
        Args:
            query: Search query
            max_results: Maximum results to return
            
        Returns:
            Dict with news search results
        """
        try:
            max_res = max_results or self.max_results
            
            with DDGS() as ddgs:
                results = []
                news_results = ddgs.news(query, max_results=max_res)
                
                for result in news_results:
                    results.append({
                        "title": result.get("title", ""),
                        "url": result.get("url", ""),
                        "snippet": result.get("body", ""),
                        "source": result.get("source", ""),
                        "date": result.get("date", ""),
                        "image": result.get("image", "")
                    })
            
            return {
                "success": True,
                "query": query,
                "total_results": len(results),
                "results": results,
                "search_type": "news"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"News search error: {str(e)}",
                "query": query,
                "results": []
            }


# Tool function for AI agent
async def web(query: str, max_results: int = 5, search_type: str = "web") -> Dict[str, Any]:
    """
    Search internet for real-time information
    
    Usage Examples:
    - web("Python 3.12 installation guide")
    - web("Docker compose tutorial 2024")
    - web("Linux memory usage commands")
    - web("latest AI news", search_type="news")
    
    Args:
        query: Search query string
        max_results: Maximum number of results to return (default: 5)
        search_type: Type of search ("web" or "news")
    
    Returns:
        Dictionary with search results
    """
    tool = WebTool()
    
    if search_type == "news":
        result = await tool.search_news(query, max_results)
    else:
        result = await tool.search(query, max_results)
    
    # Format output for AI agent
    if result["success"]:
        output = f"🌐 Web search completed\n"
        output += f"Query: {result['query']}\n"
        output += f"Results found: {result['total_results']}\n"
        
        if search_type == "news":
            output += f"Search type: News\n"
        
        output += "\n"
        
        if result["results"]:
            output += "Search Results:\n"
            for i, res in enumerate(result["results"], 1):
                output += f"\n{i}. {res['title']}\n"
                output += f"   URL: {res['url']}\n"
                output += f"   Source: {res.get('source', 'Unknown')}\n"
                
                if res.get('date'):
                    output += f"   Date: {res['date']}\n"
                
                if res['snippet']:
                    snippet = res['snippet'][:200] + "..." if len(res['snippet']) > 200 else res['snippet']
                    output += f"   Summary: {snippet}\n"
        else:
            output += "No results found for the query.\n"
    else:
        output = f"❌ Web search failed\n"
        output += f"Query: {result['query']}\n"
        output += f"Error: {result['error']}\n"
    
    return {
        "tool": "web",
        "success": result["success"],
        "output": output,
        "raw_result": result
    }
