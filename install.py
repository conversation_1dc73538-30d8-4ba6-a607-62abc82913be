#!/usr/bin/env python3
"""
Arien AI Universal Installer
Cross-platform installer for Windows 11 WSL, macOS, and Linux
"""
import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
import argparse


class ArienInstaller:
    """Universal installer for Arien AI CLI"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.is_wsl = self._detect_wsl()
        self.python_cmd = self._find_python()
        self.install_dir = self._get_install_dir()
        self.bin_dir = self._get_bin_dir()
        
    def _detect_wsl(self) -> bool:
        """Detect if running in WSL"""
        try:
            with open('/proc/version', 'r') as f:
                return 'microsoft' in f.read().lower()
        except:
            return False
    
    def _find_python(self) -> str:
        """Find appropriate Python command"""
        for cmd in ['python3', 'python']:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0 and 'Python 3.' in result.stdout:
                    return cmd
            except FileNotFoundError:
                continue
        
        raise RuntimeError("Python 3.8+ not found. Please install Python first.")
    
    def _get_install_dir(self) -> Path:
        """Get installation directory"""
        if self.system == 'windows' and not self.is_wsl:
            return Path.home() / "AppData" / "Local" / "ArienAI"
        else:
            return Path.home() / ".local" / "share" / "arien-ai"
    
    def _get_bin_dir(self) -> Path:
        """Get binary directory"""
        if self.system == 'windows' and not self.is_wsl:
            return Path.home() / "AppData" / "Local" / "ArienAI" / "bin"
        else:
            return Path.home() / ".local" / "bin"
    
    def install(self):
        """Install Arien AI CLI"""
        print("🤖 Arien AI Universal Installer")
        print("=" * 40)
        print(f"System: {self.system.title()}")
        if self.is_wsl:
            print("Environment: WSL")
        print(f"Python: {self.python_cmd}")
        print(f"Install Directory: {self.install_dir}")
        print()
        
        try:
            # Create directories
            self._create_directories()
            
            # Install Python dependencies
            self._install_dependencies()
            
            # Copy files
            self._copy_files()
            
            # Create executable script
            self._create_executable()
            
            # Update PATH
            self._update_path()
            
            print("✅ Installation completed successfully!")
            print()
            print("🚀 To get started:")
            print("   1. Restart your terminal or run: source ~/.bashrc")
            print("   2. Run: arien-ai")
            print("   3. Follow the setup wizard to configure your LLM provider")
            print()
            
        except Exception as e:
            print(f"❌ Installation failed: {e}")
            sys.exit(1)
    
    def update(self):
        """Update existing installation"""
        print("🔄 Updating Arien AI CLI...")
        
        if not self.install_dir.exists():
            print("❌ Arien AI is not installed. Run install instead.")
            sys.exit(1)
        
        try:
            # Backup configuration
            config_backup = None
            config_file = Path.home() / ".arien-ai" / "config.yaml"
            if config_file.exists():
                config_backup = config_file.read_text()
            
            # Update files
            self._copy_files()
            
            # Restore configuration
            if config_backup:
                config_file.write_text(config_backup)
            
            print("✅ Update completed successfully!")
            
        except Exception as e:
            print(f"❌ Update failed: {e}")
            sys.exit(1)
    
    def uninstall(self):
        """Uninstall Arien AI CLI"""
        print("🗑️  Uninstalling Arien AI CLI...")
        
        try:
            # Remove installation directory
            if self.install_dir.exists():
                shutil.rmtree(self.install_dir)
                print(f"✅ Removed {self.install_dir}")
            
            # Remove executable
            executable = self.bin_dir / "arien-ai"
            if executable.exists():
                executable.unlink()
                print(f"✅ Removed {executable}")
            
            # Remove configuration (ask user)
            config_dir = Path.home() / ".arien-ai"
            if config_dir.exists():
                response = input("Remove configuration and logs? (y/N): ")
                if response.lower() == 'y':
                    shutil.rmtree(config_dir)
                    print(f"✅ Removed {config_dir}")
            
            print("✅ Uninstallation completed!")
            
        except Exception as e:
            print(f"❌ Uninstallation failed: {e}")
            sys.exit(1)
    
    def _create_directories(self):
        """Create necessary directories"""
        print("📁 Creating directories...")
        
        self.install_dir.mkdir(parents=True, exist_ok=True)
        self.bin_dir.mkdir(parents=True, exist_ok=True)
        
        # Create config directory
        config_dir = Path.home() / ".arien-ai"
        config_dir.mkdir(exist_ok=True)
        (config_dir / "logs").mkdir(exist_ok=True)
        (config_dir / "backups").mkdir(exist_ok=True)
    
    def _install_dependencies(self):
        """Install Python dependencies"""
        print("📦 Installing Python dependencies...")
        
        # Check if pip is available
        try:
            subprocess.run([self.python_cmd, '-m', 'pip', '--version'], 
                         check=True, capture_output=True)
        except subprocess.CalledProcessError:
            raise RuntimeError("pip not found. Please install pip first.")
        
        # Install dependencies
        requirements_file = Path(__file__).parent / "requirements.txt"
        if requirements_file.exists():
            cmd = [self.python_cmd, '-m', 'pip', 'install', '-r', str(requirements_file)]
            subprocess.run(cmd, check=True)
        else:
            # Install individual packages
            packages = [
                'requests>=2.31.0',
                'rich>=13.7.0',
                'click>=8.1.7',
                'pyyaml>=6.0.1',
                'aiohttp>=3.9.0',
                'duckduckgo-search>=4.0.0',
                'colorama>=0.4.6',
                'prompt-toolkit>=3.0.43'
            ]
            
            for package in packages:
                cmd = [self.python_cmd, '-m', 'pip', 'install', package]
                subprocess.run(cmd, check=True)
    
    def _copy_files(self):
        """Copy application files"""
        print("📋 Copying application files...")
        
        source_dir = Path(__file__).parent
        
        # Copy source files
        src_dest = self.install_dir / "src"
        if src_dest.exists():
            shutil.rmtree(src_dest)
        shutil.copytree(source_dir / "src", src_dest)
        
        # Copy main.py
        shutil.copy2(source_dir / "main.py", self.install_dir / "main.py")
        
        # Copy requirements.txt if exists
        req_file = source_dir / "requirements.txt"
        if req_file.exists():
            shutil.copy2(req_file, self.install_dir / "requirements.txt")
    
    def _create_executable(self):
        """Create executable script"""
        print("🔧 Creating executable script...")
        
        if self.system == 'windows' and not self.is_wsl:
            # Windows batch file
            script_content = f"""@echo off
"{self.python_cmd}" "{self.install_dir / 'main.py'}" %*
"""
            script_path = self.bin_dir / "arien-ai.bat"
        else:
            # Unix shell script
            script_content = f"""#!/bin/bash
exec "{self.python_cmd}" "{self.install_dir / 'main.py'}" "$@"
"""
            script_path = self.bin_dir / "arien-ai"
        
        script_path.write_text(script_content)
        
        # Make executable on Unix systems
        if self.system != 'windows' or self.is_wsl:
            script_path.chmod(0o755)
    
    def _update_path(self):
        """Update PATH environment variable"""
        print("🛤️  Updating PATH...")
        
        if self.system == 'windows' and not self.is_wsl:
            # Windows PATH update (requires manual action)
            print(f"⚠️  Please add {self.bin_dir} to your PATH manually:")
            print("   1. Open System Properties > Environment Variables")
            print("   2. Add to PATH or create new PATH entry")
            print(f"   3. Add: {self.bin_dir}")
        else:
            # Unix PATH update
            shell_rc = self._get_shell_rc()
            path_line = f'export PATH="$PATH:{self.bin_dir}"'
            
            if shell_rc.exists():
                content = shell_rc.read_text()
                if str(self.bin_dir) not in content:
                    with open(shell_rc, 'a') as f:
                        f.write(f'\n# Arien AI CLI\n{path_line}\n')
                    print(f"✅ Updated {shell_rc}")
            else:
                shell_rc.write_text(f'# Arien AI CLI\n{path_line}\n')
                print(f"✅ Created {shell_rc}")
    
    def _get_shell_rc(self) -> Path:
        """Get shell RC file path"""
        shell = os.environ.get('SHELL', '/bin/bash')
        
        if 'zsh' in shell:
            return Path.home() / '.zshrc'
        elif 'fish' in shell:
            return Path.home() / '.config' / 'fish' / 'config.fish'
        else:
            return Path.home() / '.bashrc'


def main():
    """Main installer function"""
    parser = argparse.ArgumentParser(description='Arien AI Universal Installer')
    parser.add_argument('action', choices=['install', 'update', 'uninstall'],
                       help='Action to perform')
    
    args = parser.parse_args()
    
    installer = ArienInstaller()
    
    if args.action == 'install':
        installer.install()
    elif args.action == 'update':
        installer.update()
    elif args.action == 'uninstall':
        installer.uninstall()


if __name__ == "__main__":
    main()
