"""
Tool Executor - Manages function tool execution with parallel/sequential support
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from ..tools.bash_tool import bash
from ..tools.grep_tool import grep
from ..tools.glob_tool import glob
from ..tools.write_tool import write
from ..tools.edit_tool import edit
from ..tools.web_tool import web
from .retry_logic import RetryLogic, NETWORK_RETRY


class ExecutionMode(Enum):
    """Tool execution modes"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    AUTO = "auto"


@dataclass
class ToolCall:
    """Represents a tool function call"""
    name: str
    arguments: Dict[str, Any]
    call_id: Optional[str] = None


@dataclass
class ToolResult:
    """Result of tool execution"""
    tool_name: str
    success: bool
    output: str
    execution_time: float
    raw_result: Dict[str, Any]
    error: Optional[str] = None
    call_id: Optional[str] = None


class ToolExecutor:
    """Manages execution of AI function tools"""
    
    def __init__(self):
        self.tools = {
            "bash": bash,
            "grep": grep,
            "glob": glob,
            "write": write,
            "edit": edit,
            "web": web
        }
        self.retry_logic = RetryLogic(NETWORK_RETRY)
    
    async def execute_tool(self, tool_call: ToolCall) -> ToolResult:
        """Execute a single tool call"""
        start_time = time.time()
        
        try:
            if tool_call.name not in self.tools:
                return ToolResult(
                    tool_name=tool_call.name,
                    success=False,
                    output=f"Unknown tool: {tool_call.name}",
                    execution_time=0,
                    raw_result={},
                    error=f"Tool '{tool_call.name}' not found",
                    call_id=tool_call.call_id
                )
            
            tool_func = self.tools[tool_call.name]
            
            # Execute tool with retry logic
            result = await self.retry_logic.execute_with_retry(
                tool_func, **tool_call.arguments
            )
            
            execution_time = time.time() - start_time
            
            return ToolResult(
                tool_name=tool_call.name,
                success=result.get("success", True),
                output=result.get("output", str(result)),
                execution_time=execution_time,
                raw_result=result,
                call_id=tool_call.call_id
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return ToolResult(
                tool_name=tool_call.name,
                success=False,
                output=f"Tool execution failed: {str(e)}",
                execution_time=execution_time,
                raw_result={},
                error=str(e),
                call_id=tool_call.call_id
            )
    
    async def execute_tools(self, tool_calls: List[ToolCall], 
                           mode: ExecutionMode = ExecutionMode.AUTO) -> List[ToolResult]:
        """
        Execute multiple tool calls
        
        Args:
            tool_calls: List of tool calls to execute
            mode: Execution mode (sequential, parallel, or auto)
            
        Returns:
            List of tool results
        """
        if not tool_calls:
            return []
        
        # Determine execution mode
        if mode == ExecutionMode.AUTO:
            mode = self._determine_execution_mode(tool_calls)
        
        if mode == ExecutionMode.PARALLEL:
            return await self._execute_parallel(tool_calls)
        else:
            return await self._execute_sequential(tool_calls)
    
    async def _execute_sequential(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """Execute tools sequentially"""
        results = []
        
        for tool_call in tool_calls:
            result = await self.execute_tool(tool_call)
            results.append(result)
            
            # Stop on critical failure for dependent operations
            if not result.success and self._is_critical_failure(result):
                # Add placeholder results for remaining calls
                for remaining_call in tool_calls[len(results):]:
                    results.append(ToolResult(
                        tool_name=remaining_call.name,
                        success=False,
                        output="Skipped due to previous critical failure",
                        execution_time=0,
                        raw_result={},
                        error="Previous tool failed critically",
                        call_id=remaining_call.call_id
                    ))
                break
        
        return results
    
    async def _execute_parallel(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """Execute tools in parallel"""
        tasks = [self.execute_tool(call) for call in tool_calls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to error results
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append(ToolResult(
                    tool_name=tool_calls[i].name,
                    success=False,
                    output=f"Parallel execution error: {str(result)}",
                    execution_time=0,
                    raw_result={},
                    error=str(result),
                    call_id=tool_calls[i].call_id
                ))
            else:
                final_results.append(result)
        
        return final_results
    
    def _determine_execution_mode(self, tool_calls: List[ToolCall]) -> ExecutionMode:
        """Automatically determine best execution mode"""
        if len(tool_calls) == 1:
            return ExecutionMode.SEQUENTIAL
        
        # Check for dependencies
        tool_names = [call.name for call in tool_calls]
        
        # Sequential patterns (order matters)
        sequential_patterns = [
            ["glob", "grep"],  # Find files, then search in them
            ["glob", "edit"],  # Find files, then edit them
            ["web", "write"],  # Search web, then write results
            ["bash", "grep"],  # Run command, then search output
        ]
        
        for pattern in sequential_patterns:
            if self._matches_pattern(tool_names, pattern):
                return ExecutionMode.SEQUENTIAL
        
        # Check for file operations that might conflict
        file_tools = ["write", "edit"]
        file_operations = [call for call in tool_calls if call.name in file_tools]
        
        if len(file_operations) > 1:
            # Check if operating on same files
            file_paths = []
            for call in file_operations:
                if "filepath" in call.arguments:
                    file_paths.append(call.arguments["filepath"])
            
            if len(set(file_paths)) < len(file_paths):
                return ExecutionMode.SEQUENTIAL  # Same file, sequential
        
        # Default to parallel for independent operations
        return ExecutionMode.PARALLEL
    
    def _matches_pattern(self, tool_names: List[str], pattern: List[str]) -> bool:
        """Check if tool names match a sequential pattern"""
        if len(tool_names) < len(pattern):
            return False
        
        for i in range(len(tool_names) - len(pattern) + 1):
            if tool_names[i:i+len(pattern)] == pattern:
                return True
        
        return False
    
    def _is_critical_failure(self, result: ToolResult) -> bool:
        """Determine if a failure is critical enough to stop execution"""
        critical_errors = [
            "permission denied",
            "file not found",
            "invalid argument",
            "authentication failed"
        ]
        
        if result.error:
            error_lower = result.error.lower()
            return any(critical in error_lower for critical in critical_errors)
        
        return False
    
    def parse_tool_calls_from_response(self, response: str) -> List[ToolCall]:
        """Parse tool calls from AI response"""
        tool_calls = []

        try:
            import re
            import json

            # First try to find JSON tool calls (OpenAI format)
            json_pattern = r'\{"tool_calls":\s*\[(.*?)\]\}'
            json_matches = re.findall(json_pattern, response, re.DOTALL)

            for match in json_matches:
                try:
                    calls_data = json.loads(f'[{match}]')
                    for call_data in calls_data:
                        if 'function' in call_data and call_data['function']['name'] in self.tools:
                            tool_calls.append(ToolCall(
                                name=call_data['function']['name'],
                                arguments=call_data['function']['arguments'],
                                call_id=call_data.get('id', f"call_{len(tool_calls)}")
                            ))
                except:
                    continue

            # If no JSON found, look for function call patterns
            if not tool_calls:
                # Pattern for function calls like: tool_name("arg1", "arg2", key="value")
                pattern = r'(\w+)\s*\(\s*([^)]*)\s*\)'
                matches = re.findall(pattern, response)

                for match in matches:
                    tool_name, args_str = match

                    if tool_name in self.tools:
                        try:
                            # Parse arguments
                            args = self._parse_arguments(args_str)
                            tool_calls.append(ToolCall(
                                name=tool_name,
                                arguments=args,
                                call_id=f"{tool_name}_{len(tool_calls)}"
                            ))
                        except Exception:
                            continue

            # Also look for explicit tool usage mentions
            if not tool_calls:
                tool_calls.extend(self._parse_explicit_tool_mentions(response))

        except Exception:
            pass

        return tool_calls

    def _parse_explicit_tool_mentions(self, response: str) -> List[ToolCall]:
        """Parse explicit tool mentions in natural language"""
        tool_calls = []

        try:
            import re

            # Look for patterns like "I'll use the bash tool to..."
            for tool_name in self.tools.keys():
                patterns = [
                    rf"I'll use the {tool_name} tool to (.+?)(?:\.|$)",
                    rf"Let me use {tool_name} to (.+?)(?:\.|$)",
                    rf"Using {tool_name} to (.+?)(?:\.|$)",
                    rf"I need to {tool_name} (.+?)(?:\.|$)",
                ]

                for pattern in patterns:
                    matches = re.findall(pattern, response, re.IGNORECASE)
                    for match in matches:
                        # Extract command/query from the match
                        command = match.strip()

                        # Create appropriate arguments based on tool
                        args = self._create_args_from_description(tool_name, command)

                        if args:
                            tool_calls.append(ToolCall(
                                name=tool_name,
                                arguments=args,
                                call_id=f"{tool_name}_explicit_{len(tool_calls)}"
                            ))

        except Exception:
            pass

        return tool_calls

    def _create_args_from_description(self, tool_name: str, description: str) -> Dict[str, Any]:
        """Create tool arguments from natural language description"""
        args = {}

        try:
            if tool_name == "bash":
                # Extract command from description
                if "run" in description.lower() or "execute" in description.lower():
                    # Look for quoted commands
                    import re
                    quoted = re.findall(r'"([^"]+)"', description)
                    if quoted:
                        args["command"] = quoted[0]
                    else:
                        # Use the whole description as command
                        args["command"] = description

            elif tool_name == "web":
                # Extract search query
                args["query"] = description

            elif tool_name == "grep":
                # Try to extract pattern and files
                import re
                # Look for "search for X in Y" pattern
                match = re.search(r'search for (.+?) in (.+)', description, re.IGNORECASE)
                if match:
                    args["pattern"] = match.group(1).strip('"\'')
                    args["files"] = match.group(2).strip('"\'')
                else:
                    # Default to searching for the description
                    args["pattern"] = description
                    args["files"] = "*"

            elif tool_name == "glob":
                # Extract file pattern
                args["pattern"] = description

            elif tool_name == "write":
                # This is harder to parse from description alone
                # Would need more context
                pass

            elif tool_name == "edit":
                # This is also harder to parse
                # Would need more context
                pass

        except Exception:
            pass

        return args
    
    def _parse_arguments(self, args_str: str) -> Dict[str, Any]:
        """Parse function arguments from string"""
        args = {}

        if not args_str.strip():
            return args

        try:
            import json
            import re

            args_str = args_str.strip()

            # Try to parse as JSON first
            try:
                if args_str.startswith('{') and args_str.endswith('}'):
                    return json.loads(args_str)
            except:
                pass

            # Handle single quoted string
            if (args_str.startswith('"') and args_str.endswith('"')) or \
               (args_str.startswith("'") and args_str.endswith("'")):
                value = args_str[1:-1]
                # Determine appropriate key based on common patterns
                if any(word in value.lower() for word in ['search', 'find', 'look']):
                    return {"query": value}
                elif any(word in value.lower() for word in ['*.', '**/', 'glob']):
                    return {"pattern": value}
                else:
                    return {"command": value}

            # Parse comma-separated arguments
            # Handle nested quotes and commas properly
            parts = self._smart_split_args(args_str)

            for i, part in enumerate(parts):
                part = part.strip()

                if '=' in part and not part.startswith('"') and not part.startswith("'"):
                    # Key-value pair
                    key, value = part.split('=', 1)
                    key = key.strip().strip('"\'')
                    value = value.strip().strip('"\'')

                    # Try to convert to appropriate type
                    if value.lower() in ['true', 'false']:
                        value = value.lower() == 'true'
                    elif value.isdigit():
                        value = int(value)
                    elif value.replace('.', '').isdigit():
                        value = float(value)

                    args[key] = value
                else:
                    # Positional argument
                    value = part.strip().strip('"\'')

                    # Assign to appropriate key based on position and content
                    if i == 0:
                        if any(word in value.lower() for word in ['http', 'search', 'find']):
                            args["query"] = value
                        elif any(char in value for char in ['*', '?', '/']):
                            args["pattern"] = value if '.' in value else "command"
                            if "pattern" not in args:
                                args["pattern"] = value
                        else:
                            args["command"] = value
                    elif i == 1:
                        # Second argument is often a path or additional parameter
                        if "pattern" in args:
                            args["files"] = value
                        elif "command" in args:
                            args["directory"] = value
                        else:
                            args["files"] = value
                    else:
                        args[f"arg_{i}"] = value

        except Exception:
            # Fallback: treat entire string as single argument
            clean_str = args_str.strip().strip('"\'')
            if any(word in clean_str.lower() for word in ['search', 'find', 'http']):
                args["query"] = clean_str
            elif any(char in clean_str for char in ['*', '?']) or clean_str.endswith(('.py', '.txt', '.log')):
                args["pattern"] = clean_str
            else:
                args["command"] = clean_str

        return args

    def _smart_split_args(self, args_str: str) -> List[str]:
        """Smart split that respects quotes and nested structures"""
        parts = []
        current_part = ""
        in_quotes = False
        quote_char = None
        paren_depth = 0

        i = 0
        while i < len(args_str):
            char = args_str[i]

            if char in ['"', "'"] and not in_quotes:
                in_quotes = True
                quote_char = char
                current_part += char
            elif char == quote_char and in_quotes:
                in_quotes = False
                quote_char = None
                current_part += char
            elif char == '(' and not in_quotes:
                paren_depth += 1
                current_part += char
            elif char == ')' and not in_quotes:
                paren_depth -= 1
                current_part += char
            elif char == ',' and not in_quotes and paren_depth == 0:
                if current_part.strip():
                    parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char

            i += 1

        if current_part.strip():
            parts.append(current_part.strip())

        return parts


# Global tool executor instance
tool_executor = ToolExecutor()
