"""
Error handling utilities
"""
import traceback
import sys
from typing import Optional, Callable, Any
from functools import wraps

from .logger import default_logger


class ArienError(Exception):
    """Base exception for Arien AI"""
    pass


class ConfigurationError(ArienError):
    """Configuration related errors"""
    pass


class ProviderError(ArienError):
    """LLM provider related errors"""
    pass


class ToolError(ArienError):
    """Tool execution related errors"""
    pass


def handle_exceptions(logger=None, reraise: bool = False):
    """Decorator to handle exceptions gracefully"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                _log_exception(e, func.__name__, logger)
                if reraise:
                    raise
                return None
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                _log_exception(e, func.__name__, logger)
                if reraise:
                    raise
                return None
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def _log_exception(exception: Exception, function_name: str, logger=None):
    """Log exception details"""
    log = logger or default_logger
    
    error_msg = f"Error in {function_name}: {str(exception)}"
    log.error(error_msg)
    
    # Log full traceback in debug mode
    log.debug(f"Full traceback:\n{traceback.format_exc()}")


def setup_global_exception_handler():
    """Setup global exception handler for unhandled exceptions"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            # Allow KeyboardInterrupt to work normally
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        default_logger.critical(
            "Uncaught exception",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    sys.excepthook = handle_exception


# Error context manager
class ErrorContext:
    """Context manager for error handling"""
    
    def __init__(self, operation: str, logger=None, reraise: bool = True):
        self.operation = operation
        self.logger = logger or default_logger
        self.reraise = reraise
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_value, traceback):
        if exc_type is not None:
            self.logger.error(f"Error during {self.operation}: {exc_value}")
            
            if not self.reraise:
                return True  # Suppress exception
        
        return False  # Don't suppress exception
